const { logError, logInfo } = require('../common/logging')
const userService = require('../services/user-service')
const auditLogger = require('../common/audit-logger')
const organizationService = require('../services/admin/organization-service')
const b2cService = require('../services/b2c-service')
const { generateSecurePassword } = require('../utils/password-utils')
const jwt = require('jsonwebtoken')
const crypto = require('crypto')
const { jsonResponse } = require('../common/helper')
const fs = require('fs')
const path = require('path')

const bcrypt = require('bcrypt')
const { HttpStatusCode } = require('axios')
const rolePermissionHandler = require('../handlers/role-permission-handler')
const { paginate } = require('../utils/pagination')
const graphService = require('../services/graph-service')
const NodeCache = require('node-cache')
const { DefaultRoles } = require('../common/roles')
const cache = new NodeCache({ checkperiod: 600 })
const doctorHandler = require('./doctor-handler')
const doctorService = require('../services/doctor-service')

class UserHandler {
  async createUser(user, created_by) {
    try {
      user.created_by = created_by
      user.updated_by = created_by

      user.isActive = true
      // Ensure regular users are not marked as organization main admin
      user.isOrganizationMainAdmin = user.isOrganizationMainAdmin || false

      delete user.password
      delete user.resetToken
      delete user.resetTokenExpiry

      const temporaryPassword = generateSecurePassword()

      const b2cUser = {
        accountEnabled: true, // User is active but must change password
        displayName: user.name,
        identities: [
          {
            signInType: 'emailAddress',
            issuer: `${process.env.TENANT_NAME}.onmicrosoft.com`,
            issuerAssignedId: user.email,
          },
        ],
        passwordProfile: {
          forceChangePasswordNextSignIn: true, // Force password change on first login
          password: temporaryPassword,
        },
        passwordPolicies: 'DisablePasswordExpiration, DisableStrongPassword',
      }

      let b2cResult = null
      let res = null

      try {
        b2cResult = await b2cService.createB2CUser(b2cUser)
        logInfo(`B2C user created successfully for: ${user.email}`)

        user.b2cUserId = b2cResult.id
        res = await userService.addUser(user)
        logInfo(`Local user created successfully for: ${user.email}`)

        await b2cService.sendWelcomeEmailWithB2CSetup(
          user.email,
          user.name,
          temporaryPassword,
          false, // isAdmin = false for regular user creation
        )

        await auditLogger.logAction(
          'User Created with B2C Integration',
          created_by,
          {
            userId: res.id,
            b2cUserId: b2cResult.id,
          },
        )

        logInfo(`User creation completed successfully for: ${user.email}`)
      } catch (error) {
        logError(`User creation failed for: ${user.email}`, error)

        // Rollback: Clean up any created resources
        if (res) {
          try {
            await userService.deleteUser(res.id)
            logInfo(`Cleaned up local user after error for: ${user.email}`)
          } catch (cleanupError) {
            logError(
              `Failed to cleanup local user for: ${user.email}`,
              cleanupError,
            )
          }
        }

        if (b2cResult) {
          try {
            await b2cService.deleteB2CUser(b2cResult.id)
            logInfo(`Cleaned up B2C user after error for: ${user.email}`)
          } catch (cleanupError) {
            logError(
              `Failed to cleanup B2C user for: ${user.email}`,
              cleanupError,
            )
          }
        }

        // Preserve GraphError and Microsoft Graph API error properties for proper handling upstream
        if (
          error.name === 'GraphError' ||
          error.code === 'Request_BadRequest'
        ) {
          throw error // Re-throw error as-is
        }

        // For other errors, wrap them but preserve important properties
        const wrappedError = new Error(
          `Failed to create user: ${error.message}`,
        )
        wrappedError.code = error.code || 'USER_CREATION_FAILED'
        wrappedError.statusCode = error.statusCode || 500
        wrappedError.originalError = error
        throw wrappedError
      }

      return res
    } catch (error) {
      logError(`Unable to create user`, error)

      // Re-throw GraphError and other important errors for proper handling upstream
      if (
        error.name === 'GraphError' ||
        (error.name === 'Error' && error.code === 'Request_BadRequest') ||
        error.code === 'USER_ALREADY_EXISTS'
      ) {
        throw error
      }

      // For other errors, return null (existing behavior)
      return null
    }
  }

  generatePKCEChallenge() {
    // Generate code verifier (43-128 characters)
    const codeVerifier = crypto.randomBytes(32).toString('base64url')

    // Generate code challenge (SHA256 hash of verifier, base64url encoded)
    const codeChallenge = crypto
      .createHash('sha256')
      .update(codeVerifier)
      .digest('base64url')

    return { codeVerifier, codeChallenge }
  }

  async activateUser(token) {
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET)
      const email = decoded.email
      const user = await userService.getUserByEmail(email)
      if (user && user.length > 0) {
        const updatedUser = {
          ...user[0],
          isActive: true,
          resetToken: null,
        }
        await userService.updateUser(updatedUser)
        await auditLogger.logAction('User Activated', email, {
          userId: updatedUser.id,
        })
        return updatedUser
      }
      return null
    } catch (error) {
      logError(`Unable to activate user`, error)
      return null
    }
  }

  async getUserByEmail(email) {
    var data = await userService.getUserByEmail(email)
    return data
  }

  async getUserDetailsByEmail(email) {
    try {
      const user = await userService.getUserByEmail(email)
      if (!user || user.length === 0) {
        return null
      }

      const userData = user[0]

      let organizationName = null
      if (userData.organizationId) {
        try {
          const organization = await organizationService.getOrganizationById(
            userData.organizationId,
          )
          organizationName = organization?.name || null
        } catch (error) {
          logError('Error fetching organization name', error)
        }
      }

      let permissionKeys = []
      if (userData.roleId) {
        const rolePermissions = await rolePermissionHandler.getAPIListbyRoleId(
          userData.roleId,
        )
        permissionKeys = [
          ...new Set(
            rolePermissions?.APIs.map(
              (apiPermission) => apiPermission.permissionKey,
            ) || [],
          ),
        ]
      }

      let redirectUrl = null
      if (permissionKeys.includes('emr.access')) {
        redirectUrl = `${process.env.BASE_URL}/emr`
      } else if (permissionKeys.includes('mrd.access')) {
        redirectUrl = `${process.env.BASE_URL}/mrd`
      } else {
        redirectUrl = `${process.env.BASE_URL}/no-access`
      }

      const response = {
        id: userData.id,
        email: userData.email,
        userRole: userData.userRole,
        name: userData.name,
        roleId: userData.roleId,
        organizationId: userData.organizationId,
        organizationName,
        userType: userData.userType,
        isActive: userData.isActive,
        b2cUserId: userData.b2cUserId,
        redirectUrl,
        permissionKeys,
      }

      return response
    } catch (error) {
      logError('Error getting user details by email', error)
      return null
    }
  }

  async getAllUser(pageSize, continuetoken) {
    var data = await userService.getAllUser(pageSize, continuetoken)
    return data
  }

  async getUserByUserType(userType) {
    var query = `SELECT * FROM c WHERE c.userType = '${userType}'`
    var data = await userService.getUserByQuery(query)
    return data
  }

  async getUserCount() {
    var query = `SELECT VALUE COUNT(1) FROM c`
    var data = await userService.getUserByQuery(query)
    return data
  }

  async loginUser(req) {
    try {
      const { email, password } = await req.json()

      if (!email || !password) {
        return jsonResponse('Email and password are required', 400)
      }

      const user = await userService.getUserByEmail(email)
      if (!user || user.length === 0) {
        return jsonResponse('Invalid email or password', 401)
      }

      const hashedPassword = user[0]?.password
      if (!hashedPassword) {
        return jsonResponse('Password not set for this user', 400)
      }

      const isPasswordValid = await userService.comparePassword(
        password,
        hashedPassword,
      )

      if (!isPasswordValid) {
        return jsonResponse('Invalid email or password', 401)
      }

      if (!user[0].isActive) {
        return jsonResponse('User account is inactive', 403)
      }

      // Load RSA private key for signing the token
      const privateKey = fs.readFileSync(
        path.resolve(__dirname, '../keys/private.pem'),
        'utf8',
      )

      // Generate a token with the required claims
      const payload = {
        exp: Math.floor(Date.now() / 1000) + 3600, // Token expires in 1 hour
        nbf: Math.floor(Date.now() / 1000), // Token is valid from now
        ver: '1.0',
        iss: `https://${process.env.TENANT_NAME}.b2clogin.com/${process.env.TENANT_ID}/v2.0/`,
        sub: user[0].id,
        aud: process.env.CLIENT_ID,
        nonce: crypto.randomBytes(16).toString('hex'),
        iat: Math.floor(Date.now() / 1000),
        auth_time: Math.floor(Date.now() / 1000),
        oid: user[0].id,
        name: user[0].name,
        emails: [user[0].email],
        tfp: process.env.signin_policy,
      }

      const token = jwt.sign(payload, privateKey, {
        algorithm: 'RS256',
      })

      const rolePermissions = await rolePermissionHandler.getAPIListbyRoleId(
        user[0].roleId,
      )

      const permissionKeys = [
        ...new Set(
          rolePermissions?.APIs.map(
            (apiPermission) => apiPermission.permissionKey,
          ) || [],
        ),
      ]

      let redirectUrl = null
      if (permissionKeys.includes('emr.access')) {
        redirectUrl = `${process.env.BASE_URL}/emr`
      } else if (permissionKeys.includes('mrd.access')) {
        redirectUrl = `${process.env.BASE_URL}/mrd`
      } else {
        redirectUrl = `${process.env.BASE_URL}/no-access`
      }

      const response = {
        token,
        user: {
          id: user[0].id,
          email: user[0].email,
          role: user[0].userRole,
          name: user[0].name,
          roleId: user[0].roleId,
          organizationId: user[0].organizationId,
        },
        redirectUrl,
        permissionKeys,
      }

      return {
        status: HttpStatusCode.Ok,
        body: response,
      }
    } catch (error) {
      logError('Error during user login', error)
      return jsonResponse('Error during login', 500)
    }
  }

  async setPassword(req) {
    try {
      const { resetToken, password, confirmPassword } = await req.json()

      if (!resetToken || !password || !confirmPassword) {
        return jsonResponse(
          'Activation token, password, and confirm password are required',
          400,
        )
      }

      if (password !== confirmPassword) {
        return jsonResponse('Password and confirm password do not match', 422)
      }

      const user = await userService.validateActivationToken(resetToken)

      if (!user) {
        return jsonResponse('Invalid or expired activation token', 400)
      }

      await userService.updatePasswordAndActivateUser(user.id, password)

      return jsonResponse(
        { message: 'Password set successfully. User activated.' },
        200,
      )
    } catch (error) {
      logError('Error setting password', error)
      return jsonResponse('Error setting password', 500)
    }
  }

  async hashPassword(password) {
    try {
      return await bcrypt.hash(password, 10)
    } catch (error) {
      logError('Error hashing password', error)
      throw error
    }
  }

  async getUsersByOrganization(req) {
    try {
      const organizationId = req.query.get('organizationId') // Optional now
      const search = req.query.get('search') || ''
      const role = req.query.get('role')
      const isActive = req.query.get('isActive') || null
      const sortBy = req.query.get('sortBy') || 'name'
      const sortOrder = req.query.get('sortOrder') || 'asc'
      const pageSize = parseInt(req.query.get('pageSize')) || 10
      const page = parseInt(req.query.get('page')) || 1
      const continueToken = req.query.get('continueToken')

      const result = await userService.getUsersByOrganization(
        organizationId,
        search,
        role,
        isActive,
        sortBy,
        sortOrder,
        pageSize,
        continueToken,
      )

      const items = result?.items || []

      // Add organization names to users
      const itemsWithOrgNames = await this.addOrganizationNamesToUsers(items)

      const paginatedResult = paginate(itemsWithOrgNames, pageSize, page)

      const response = {
        ...paginatedResult,
        scope: organizationId ? 'organization' : 'all_organizations',
        organizationId: organizationId || null,
      }

      return jsonResponse(response, 200)
    } catch (error) {
      logError('Error fetching users', error)
      return jsonResponse('Error fetching users', 500)
    }
  }

  async addOrganizationNamesToUsers(users) {
    if (!users || users.length === 0) {
      return users
    }

    try {
      const orgIds = [
        ...new Set(users.map((user) => user.organizationId).filter(Boolean)),
      ]

      const orgMap = new Map()

      for (const orgId of orgIds) {
        try {
          const organization = await organizationService.getOrganizationById(
            orgId,
          )
          if (organization) {
            orgMap.set(orgId, {
              name: organization.name,
              contactEmail: organization.contactEmail,
              isActive: organization.isActive,
            })
          }
        } catch (error) {
          logError(`Error fetching organization ${orgId}:`, error)
        }
      }

      const usersWithOrgNames = users.map((user) => {
        const orgDetails = orgMap.get(user.organizationId)
        return {
          ...user,
          organizationName: orgDetails?.name || 'Unknown Organization',
          organizationContactEmail: orgDetails?.contactEmail || null,
          organizationIsActive: orgDetails?.isActive || false,
        }
      })

      logInfo(`Added organization names to ${usersWithOrgNames.length} users`)
      return usersWithOrgNames
    } catch (error) {
      logError('Error adding organization names to users:', error)
      return users
    }
  }

  async getActiveDoctorsByUserOrganization(userId) {
    try {
      const user = await userService.getUserById(userId)
      if (!user || !user.organizationId) {
        throw new Error('User organization not found. Cannot get doctors.')
      }

      const query = `SELECT c.id, c.name, c.email, c.userType, c.userRole, c.organizationId, c.isActive FROM c WHERE c.userType = 'doctor' AND c.isActive = true AND c.organizationId = '${user.organizationId}'`
      const data = await userService.getUserByQuery(query)

      logInfo(
        `Retrieved ${data?.length || 0} active doctors for organization ${
          user.organizationId
        }`,
      )
      return data
    } catch (error) {
      logError('Error fetching active doctors by user organization', error)
      return null
    }
  }

  async updateUser(userId, updateData, updatedBy) {
    try {
      logInfo(`Updating user: ${userId}`)

      const existingUser = await userService.getUserById(userId)
      if (!existingUser) {
        logError(`User not found: ${userId}`)
        return null
      }

      const updatedUser = {
        ...existingUser,
        ...updateData,
        updated_by: updatedBy,
        updated_on: new Date().toISOString(),
      }

      const result = await userService.updateUser(updatedUser)

      if (updateData.name && updateData.name !== existingUser.name) {
        try {
          logInfo(`Updating B2C displayName for user: ${userId}`)

          var Token = cache.get(`graphToken`)
          if (!Token || Token.expiresOn.getTime() < new Date().getTime()) {
            Token = await graphService.getToken()
            cache.set('graphToken', {
              accessToken: Token.accessToken,
              expiresOn: Token.expiresOn,
            })
          }

          await graphService.updateUser(Token.accessToken, userId, updateData)
          logInfo(`B2C displayName updated successfully for user: ${userId}`)
        } catch (b2cError) {
          logError(
            `Failed to update B2C displayName for user ${userId}:`,
            b2cError,
          )
        }
      }

      logInfo(`User updated successfully: ${userId}`)
      return result
    } catch (error) {
      logError('Error updating user', error)
      throw error
    }
  }

  async deleteUser(userId, deleted_by) {
    try {
      logInfo(`Attempting to delete user: ${userId}`)

      const existingUser = await userService.getUserById(userId)
      if (!existingUser) {
        return {
          success: false,
          message: 'User not found',
          statusCode: 404,
        }
      }

      if (existingUser.userRole === DefaultRoles.SUPER_ADMIN) {
        return {
          success: false,
          message: 'Super Admin users cannot be deleted',
          statusCode: 403,
        }
      }

      if (existingUser.b2cUserId) {
        try {
          logInfo(`Deleting user from Azure B2C: ${existingUser.b2cUserId}`)

          var Token = cache.get(`graphToken`)
          if (!Token || Token.expiresOn.getTime() < new Date().getTime()) {
            Token = await graphService.getToken()
            cache.set('graphToken', {
              accessToken: Token.accessToken,
              expiresOn: Token.expiresOn,
            })
          }

          await graphService.deleteUser(
            Token.accessToken,
            existingUser.b2cUserId,
          )
          logInfo(
            `User deleted from Azure B2C successfully: ${existingUser.b2cUserId}`,
          )
        } catch (b2cError) {
          logError(
            `Failed to delete user from Azure B2C: ${existingUser.b2cUserId}`,
            b2cError,
          )

          if (b2cError.statusCode !== 404) {
            return {
              success: false,
              message: 'Failed to delete user from Azure B2C',
              statusCode: 500,
            }
          }
          logInfo(
            `User not found in Azure B2C (404), continuing with local deletion: ${existingUser.b2cUserId}`,
          )
        }
      } else {
        logInfo(`User ${userId} has no B2C ID, skipping B2C deletion`)
      }

      // Check if user is a doctor and delete corresponding doctor profile
      if (existingUser.userRole === DefaultRoles.DOCTOR) {
        try {
          logInfo(
            `User is a doctor, attempting to delete doctor profile for email: ${existingUser.email}`,
          )
          const doctorProfile = await doctorHandler.getDoctorByEmail(
            existingUser.email,
          )

          if (doctorProfile) {
            await doctorService.deleteDoctor(doctorProfile.id)
            logInfo(
              `Doctor profile deleted successfully for email: ${existingUser.email}`,
            )
          } else {
            logInfo(`No doctor profile found for email: ${existingUser.email}`)
          }
        } catch (doctorDeleteError) {
          logError(
            `Failed to delete doctor profile for user ${userId}:`,
            doctorDeleteError,
          )
          // Don't fail the user deletion if doctor profile deletion fails
        }
      }

      logInfo(`Starting local database deletion for user: ${userId}`)
      try {
        const deleteResult = await userService.deleteUser(userId)
        logInfo(
          `Local database deletion completed successfully for user: ${userId}`,
        )
        logInfo(`Delete operation result:`, deleteResult)
      } catch (error) {
        logError(`Local database deletion failed for user: ${userId}`, error)
        return {
          success: false,
          message: 'Failed to delete user from local database',
          statusCode: 500,
        }
      }

      logInfo(`User deleted successfully: ${userId}`)
      return {
        success: true,
        deletedUser: {
          id: existingUser.id,
          email: existingUser.email,
          name: existingUser.name,
        },
      }
    } catch (error) {
      logError(`Error deleting user ${userId}:`, error)
      return {
        success: false,
        message: 'An unexpected error occurred while deleting the user',
        statusCode: 500,
      }
    }
  }
}

module.exports = new UserHandler()
