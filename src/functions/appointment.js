const { app } = require('@azure/functions')
const { HttpMethod } = require('../common/constant')
const appointmentManageHandler = require('../handlers/appointment-manage-handler')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const { paginate } = require('../utils/pagination')

app.http('appointment', {
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
  authLevel: 'function',
  handler: async (req, context) => {
    context.log(`Http function processed req for url "${req.url}"`)

    const decode = context.extraInputs.get('decode')

    switch (req.method) {
      case HttpMethod.get:
        var appointmentId = req.query.get('appointmentId')
        var pageSize = parseInt(req.query.get('pageSize')) || 10
        var page = parseInt(req.query.get('page')) || 1
        var searchName = req.query.get('patientName') || ''
        var searchId = req.query.get('patientId') || ''
        let statusParamRaw = req.query.getAll('status') || []
        let statusParam = []

        statusParamRaw.forEach((item) => {
          statusParam.push(...item.split(',').map((s) => s.trim()))
        })

        if (appointmentId) {
          var data = await appointmentManageHandler.getAppointmentDetails(
            appointmentId,
          )

          if (data && data.queues) {
            let filteredQueues = data.queues
            if (searchName) {
              filteredQueues = filteredQueues.filter(
                (queue) =>
                  queue.patientName &&
                  queue.patientName
                    .toLowerCase()
                    .includes(searchName.toLowerCase()),
              )
            }

            if (searchId) {
              filteredQueues = filteredQueues.filter(
                (queue) =>
                  queue.patientId &&
                  queue.patientId.toString() === searchId.toString(),
              )
            }
            if (statusParam.length > 0) {
              filteredQueues = filteredQueues.filter((queue) =>
                statusParam.includes(queue.status),
              )
            }
            data.queues = filteredQueues
            const consultationBookedRecords = data.queues.filter(
              (queue) => queue.status === 'Consultation-Booked',
            )
            const otherRecords = data.queues.filter(
              (queue) => queue.status !== 'Consultation-Booked',
            )

            otherRecords.sort((a, b) => a.queuePosition - b.queuePosition)

            const otherQueuePositionOne = otherRecords.find(
              (record) => record.queuePosition === 1,
            )
            const filteredOtherRecords = otherRecords.filter(
              (record) => record.queuePosition !== 1,
            )

            const totalQueueCount = data.queues.length
            const totalPages = Math.ceil(totalQueueCount / pageSize) // Calculate total pages

            let paginatedRecords = []
            if (page === 1) {
              consultationBookedRecords.forEach((record) => {
                record.queuePosition = record.queuePosition || 1
              })
              const adjustedOtherRecords = filteredOtherRecords.map(
                (record) => ({
                  ...record,
                  queuePosition: record.queuePosition,
                }),
              )
              paginatedRecords = [
                ...consultationBookedRecords,
                ...(otherQueuePositionOne ? [otherQueuePositionOne] : []),
                ...adjustedOtherRecords.slice(
                  0,
                  pageSize -
                    consultationBookedRecords.length -
                    (otherQueuePositionOne ? 1 : 0),
                ),
              ]
            } else {
              const recordsOnFirstPage =
                consultationBookedRecords.length +
                (otherQueuePositionOne ? 1 : 0)
              const adjustedStartIndex =
                (page - 1) * pageSize - recordsOnFirstPage
              const startIndex = Math.max(adjustedStartIndex, 0)
              const endIndex = startIndex + pageSize
              paginatedRecords = filteredOtherRecords
                .slice(startIndex, endIndex)
                .map((record) => ({
                  ...record,
                  queuePosition: record.queuePosition,
                }))
            }

            data.queues = paginatedRecords
            data.totalQueueCount = totalQueueCount
            data.totalPages = totalPages // Add totalPages to the response
            data.currentPage = page
          }

          return jsonResponse(data)
        }

        var doctorId = req.query.get('doctorId'),
          patientId = req.query.get('patientId')
        var date = req.query.get('date')
        var data = null
        if (date && doctorId) {
          data = await appointmentManageHandler.getAppointment(doctorId, date)
        } else if (doctorId) {
          data = await appointmentManageHandler.getAppointments(doctorId)
        } else {
          return jsonResponse(`Missing doctorId`, HttpStatusCode.BadRequest)
        }

        if (Array.isArray(data)) {
          const consultationBookedRecords = data.filter(
            (appointment) => appointment.status === 'Consultation-Booked',
          )
          const otherRecords = data.filter(
            (appointment) => appointment.status !== 'Consultation-Booked',
          )

          otherRecords.sort((a, b) => a.queuePosition - b.queuePosition)

          const otherQueuePositionOne = otherRecords.find(
            (record) => record.queuePosition === 1,
          )
          const filteredOtherRecords = otherRecords.filter(
            (record) => record.queuePosition !== 1,
          )

          const totalQueueCount = data.length
          const totalPages = Math.ceil(totalQueueCount / pageSize) // Calculate total pages

          let paginatedRecords = []
          if (page === 1) {
            consultationBookedRecords.forEach((record) => {
              record.queuePosition = 1
            })
            const adjustedOtherRecords = filteredOtherRecords.map(
              (record, index) => ({
                ...record,
                queuePosition: index + 2,
              }),
            )
            paginatedRecords = [
              ...consultationBookedRecords,
              ...(otherQueuePositionOne ? [otherQueuePositionOne] : []),
              ...adjustedOtherRecords.slice(
                0,
                pageSize +
                  1 -
                  consultationBookedRecords.length -
                  (otherQueuePositionOne ? 1 : 0),
              ),
            ]
          } else {
            const recordsOnFirstPage =
              consultationBookedRecords.length + (otherQueuePositionOne ? 1 : 0)
            const adjustedStartIndex =
              (page - 1) * pageSize - recordsOnFirstPage
            const startIndex = Math.max(adjustedStartIndex, 0)
            const endIndex = startIndex + pageSize
            paginatedRecords = filteredOtherRecords
              .slice(startIndex, endIndex)
              .map((record, index) => ({
                ...record,
                queuePosition: recordsOnFirstPage + startIndex + index + 1,
              }))
          }

          data = {
            queues: paginatedRecords,
            totalQueueCount,
            totalPages, // Add totalPages to the response
            currentPage: page,
          }
        }

        return jsonResponse(data)

      case HttpMethod.post:
      case HttpMethod.put:
        if (!req.body) {
          return jsonResponse(
            `Missing appoinment payload`,
            HttpStatusCode.BadRequest,
          )
        }
        var doctorId = req.query.get('doctorId')
        var appointment = await req.json()
        var result = null
        if (!doctorId) {
          return jsonResponse(`Missing doctorId`, HttpStatusCode.BadRequest)
        }
        if (req.method == HttpMethod.post) {
          appointment.doctorId = doctorId
          result = await appointmentManageHandler.addAppointment(
            appointment,
            decode.oid,
          )
        } else {
          result = await appointmentManageHandler.updateAppointment(
            appointment,
            decode.oid,
          )
        }
        return jsonResponse(result)
      case HttpMethod.patch:
        if (!req.body) {
          return jsonResponse(
            `Missing appoinment payload`,
            HttpStatusCode.BadRequest,
          )
        }
        var appointmentId = req.query.get('appointmentId')
        var appointment = await req.json()
        var result = null
        if (!appointmentId) {
          return jsonResponse(
            `Missing appointmentId`,
            HttpStatusCode.BadRequest,
          )
        }
        result = await appointmentManageHandler.upsertAppointment(
          appointmentId,
          appointment,
        )
        return jsonResponse(result)

      case HttpMethod.delete:
        let queueId = req.query.get('queueId')

        var deleteResponse = null
        if (!queueId) {
          return jsonResponse(`Missing queueId`, HttpStatusCode.BadRequest)
        }
        deleteResponse = await appointmentManageHandler.deleteQueue(queueId)
        return jsonResponse(deleteResponse)
      default:
        return jsonResponse(
          `Unsupported HTTP method`,
          HttpStatusCode.MethodNotAllowed,
        )
    }
  },
})
